# ESP32串口通信语音控制LED测试说明

## 项目概述

本项目实现了通过语音控制另一块ESP32上的LED灯。小智ESP32接收语音指令，解析后通过串口发送控制命令给接收端ESP32，接收端ESP32根据命令控制LED的开关和亮度。

## 硬件连接

### 小智ESP32 (发送端)
- TX: GPIO_NUM_17
- RX: GPIO_NUM_18
- 波特率: 115200

### 接收端ESP32
- TX: GPIO_NUM_17 (连接到小智ESP32的RX)
- RX: GPIO_NUM_16 (连接到小智ESP32的TX)
- LED: GPIO_NUM_2
- 波特率: 115200

### 连接方式
```
小智ESP32          接收端ESP32
GPIO_17 (TX) ----> GPIO_16 (RX)
GPIO_18 (RX) <---- GPIO_17 (TX)
GND          ----> GND
```

## 软件实现

### 小智ESP32端
1. **UartCommunication类**: 负责串口通信的初始化、发送和接收
2. **RemoteLed IoT设备**: 注册为IoT设备，可被语音指令控制
3. **支持的方法**:
   - `TurnOn`: 打开远程LED
   - `TurnOff`: 关闭远程LED
   - `Toggle`: 切换LED状态
   - `SetBrightness`: 设置LED亮度

### 接收端ESP32
1. **UART事件处理**: 监听串口数据并解析命令
2. **LED控制**: 根据命令控制GPIO_NUM_2上的LED
3. **响应机制**: 向小智ESP32发送执行结果

## 支持的语音指令

用户可以说以下指令来控制远程LED：

### 开灯指令
- "打开灯"
- "开启灯"
- "开灯"
- "点亮灯"

### 关灯指令
- "关闭灯"
- "关灯"
- "熄灭灯"

### 切换指令
- "切换灯"
- "开关灯"

## 串口命令格式

### 发送的命令 (小智ESP32 -> 接收端ESP32)
- `led turn on` - 打开LED
- `led turn off` - 关闭LED
- `led brightness <0-100>` - 设置LED亮度
- `led status` - 查询LED状态

### 接收的响应 (接收端ESP32 -> 小智ESP32)
- `LED_ON_OK` - LED开启成功
- `LED_OFF_OK` - LED关闭成功
- `BRIGHTNESS_<value>_OK` - 亮度设置成功
- `LED_STATUS_ON/OFF` - LED状态查询结果
- `HEARTBEAT` - 心跳信号
- `RECEIVER_READY` - 接收端就绪
- `UNKNOWN_CMD` - 未知命令

## 测试步骤

### 1. 准备硬件
1. 准备两块ESP32开发板
2. 按照上述连接方式连接串口线和地线
3. 在接收端ESP32的GPIO_NUM_2连接一个LED和限流电阻

### 2. 烧录程序
1. 将小智ESP32项目编译并烧录到第一块ESP32
2. 将`receiver_esp32_example.cpp`编译并烧录到第二块ESP32

### 3. 测试语音控制
1. 启动两块ESP32
2. 确保小智ESP32连接到网络并激活
3. 对小智ESP32说："打开灯"
4. 观察接收端ESP32的LED是否点亮
5. 对小智ESP32说："关闭灯"
6. 观察接收端ESP32的LED是否熄灭

### 4. 调试信息
- 小智ESP32会在日志中显示发送的串口命令
- 接收端ESP32会在日志中显示接收到的命令和执行结果
- 可以通过串口监视器查看两端的调试信息

## 故障排除

### 1. 串口通信问题
- 检查TX/RX连接是否正确（交叉连接）
- 确认波特率设置一致（115200）
- 检查地线连接

### 2. 语音识别问题
- 确保小智ESP32网络连接正常
- 检查IoT设备是否正确注册
- 尝试不同的语音指令表达方式

### 3. LED控制问题
- 检查LED和电阻连接
- 确认GPIO配置正确
- 查看接收端ESP32的日志输出

## 扩展功能

可以进一步扩展以下功能：
1. 支持多个LED控制
2. 添加PWM亮度控制
3. 支持RGB LED颜色控制
4. 添加传感器数据回传
5. 实现更复杂的设备控制协议

## 注意事项

1. 确保两块ESP32共地连接
2. 串口线不要过长，避免信号干扰
3. 可以添加光耦隔离提高系统稳定性
4. 在实际应用中建议添加错误重试机制
