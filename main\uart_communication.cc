#include "uart_communication.h"
#include <esp_log.h>
#include <string.h>

#define TAG "UartCommunication"
#define UART_BUFFER_SIZE 1024
#define UART_QUEUE_SIZE 20

UartCommunication::UartCommunication(uart_port_t uart_port, gpio_num_t tx_pin, gpio_num_t rx_pin, int baud_rate)
    : uart_port_(uart_port), tx_pin_(tx_pin), rx_pin_(rx_pin), baud_rate_(baud_rate),
      receive_task_handle_(nullptr), is_running_(false), receive_callback_(nullptr) {
}

UartCommunication::~UartCommunication() {
    Stop();
}

bool UartCommunication::Initialize() {
    // Configure UART parameters
    uart_config_t uart_config = {
        .baud_rate = baud_rate_,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // Install UART driver
    esp_err_t ret = uart_driver_install(uart_port_, UART_BUFFER_SIZE * 2, UART_BUFFER_SIZE * 2, UART_QUEUE_SIZE, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return false;
    }

    // Configure UART parameters
    ret = uart_param_config(uart_port_, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(uart_port_);
        return false;
    }

    // Set UART pins
    ret = uart_set_pin(uart_port_, tx_pin_, rx_pin_, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(uart_port_);
        return false;
    }

    ESP_LOGI(TAG, "UART%d initialized successfully on TX:%d, RX:%d, baud:%d", 
             uart_port_, tx_pin_, rx_pin_, baud_rate_);
    return true;
}

bool UartCommunication::SendCommand(const std::string& command) {
    if (!is_running_) {
        ESP_LOGW(TAG, "UART communication is not running");
        return false;
    }

    std::string cmd_with_newline = command + "\n";
    int bytes_written = uart_write_bytes(uart_port_, cmd_with_newline.c_str(), cmd_with_newline.length());
    
    if (bytes_written < 0) {
        ESP_LOGE(TAG, "Failed to send command: %s", command.c_str());
        return false;
    }

    ESP_LOGI(TAG, "Sent command: %s", command.c_str());
    return true;
}

void UartCommunication::SetReceiveCallback(std::function<void(const std::string&)> callback) {
    receive_callback_ = callback;
}

void UartCommunication::Start() {
    if (is_running_) {
        ESP_LOGW(TAG, "UART communication is already running");
        return;
    }

    is_running_ = true;
    
    // Create receive task
    xTaskCreate(ReceiveTask, "uart_receive", 4096, this, 5, &receive_task_handle_);
    ESP_LOGI(TAG, "UART communication started");
}

void UartCommunication::Stop() {
    if (!is_running_) {
        return;
    }

    is_running_ = false;

    // Delete receive task
    if (receive_task_handle_ != nullptr) {
        vTaskDelete(receive_task_handle_);
        receive_task_handle_ = nullptr;
    }

    // Uninstall UART driver
    uart_driver_delete(uart_port_);
    ESP_LOGI(TAG, "UART communication stopped");
}

void UartCommunication::ReceiveTask(void* parameter) {
    UartCommunication* uart_comm = static_cast<UartCommunication*>(parameter);
    uart_comm->ProcessReceiveData();
}

void UartCommunication::ProcessReceiveData() {
    uint8_t data[UART_BUFFER_SIZE];
    std::string buffer;

    while (is_running_) {
        int length = uart_read_bytes(uart_port_, data, UART_BUFFER_SIZE - 1, pdMS_TO_TICKS(100));
        
        if (length > 0) {
            data[length] = '\0';
            buffer += std::string((char*)data);

            // Process complete lines
            size_t pos = 0;
            while ((pos = buffer.find('\n')) != std::string::npos) {
                std::string line = buffer.substr(0, pos);
                buffer.erase(0, pos + 1);

                // Remove carriage return if present
                if (!line.empty() && line.back() == '\r') {
                    line.pop_back();
                }

                if (!line.empty() && receive_callback_) {
                    ESP_LOGI(TAG, "Received: %s", line.c_str());
                    receive_callback_(line);
                }
            }
        }
    }
}
