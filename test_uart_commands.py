#!/usr/bin/env python3
"""
ESP32串口通信测试脚本
用于测试小智ESP32与接收端ESP32之间的串口通信

使用方法:
1. 连接小智ESP32到电脑
2. 运行此脚本模拟发送串口命令
3. 观察接收端ESP32的LED响应

注意: 需要安装pyserial库
pip install pyserial
"""

import serial
import time
import sys

def test_uart_communication(port, baudrate=115200):
    """
    测试UART通信
    
    Args:
        port: 串口端口号 (如 'COM3' 或 '/dev/ttyUSB0')
        baudrate: 波特率，默认115200
    """
    try:
        # 打开串口
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"已连接到串口: {port}, 波特率: {baudrate}")
        
        # 等待连接稳定
        time.sleep(2)
        
        # 测试命令列表
        test_commands = [
            "led turn on",      # 开灯
            "led turn off",     # 关灯
            "led turn on",      # 再次开灯
            "led brightness 50", # 设置亮度50%
            "led brightness 100", # 设置亮度100%
            "led brightness 0",  # 设置亮度0%
            "led status",       # 查询状态
            "led turn off",     # 关灯
            "unknown command",  # 测试未知命令
        ]
        
        print("\n开始测试串口命令...")
        print("=" * 50)
        
        for i, command in enumerate(test_commands, 1):
            print(f"\n测试 {i}/{len(test_commands)}: 发送命令 '{command}'")
            
            # 发送命令
            ser.write((command + '\n').encode())
            ser.flush()
            
            # 等待响应
            time.sleep(1)
            
            # 读取响应
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting).decode().strip()
                print(f"接收到响应: {response}")
            else:
                print("未收到响应")
            
            # 等待一段时间再发送下一个命令
            time.sleep(2)
        
        print("\n=" * 50)
        print("测试完成!")
        
        # 保持连接，监听心跳和其他消息
        print("\n监听心跳和其他消息 (按Ctrl+C退出)...")
        try:
            while True:
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting).decode().strip()
                    if data:
                        print(f"收到消息: {data}")
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n用户中断，退出监听")
        
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        print("请检查:")
        print("1. 串口端口号是否正确")
        print("2. ESP32是否已连接")
        print("3. 串口是否被其他程序占用")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print("串口已关闭")

def list_available_ports():
    """列出可用的串口端口"""
    import serial.tools.list_ports
    
    ports = serial.tools.list_ports.comports()
    if ports:
        print("可用的串口端口:")
        for i, port in enumerate(ports, 1):
            print(f"{i}. {port.device} - {port.description}")
        return [port.device for port in ports]
    else:
        print("未找到可用的串口端口")
        return []

def main():
    print("ESP32串口通信测试工具")
    print("=" * 30)
    
    # 列出可用端口
    available_ports = list_available_ports()
    
    if not available_ports:
        print("请连接ESP32设备后重试")
        return
    
    # 让用户选择端口
    if len(available_ports) == 1:
        selected_port = available_ports[0]
        print(f"\n自动选择端口: {selected_port}")
    else:
        print("\n请选择要使用的串口端口:")
        try:
            choice = int(input("输入端口编号: ")) - 1
            if 0 <= choice < len(available_ports):
                selected_port = available_ports[choice]
            else:
                print("无效的选择")
                return
        except ValueError:
            print("请输入有效的数字")
            return
    
    # 开始测试
    test_uart_communication(selected_port)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序发生错误: {e}")
