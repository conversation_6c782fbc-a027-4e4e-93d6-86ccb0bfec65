#include "iot/thing.h"
#include "uart_communication.h"
#include <esp_log.h>
#include <memory>

#define TAG "RemoteLed"

namespace iot {

// 远程LED控制设备类
class RemoteLed : public Thing {
private:
    static std::unique_ptr<UartCommunication> uart_comm_;
    bool power_ = false;

    // 初始化UART通信
    static void InitializeUart() {
        if (uart_comm_ == nullptr) {
            // 使用UART1，TX引脚GPIO_NUM_17，RX引脚GPIO_NUM_18，波特率115200
            uart_comm_ = std::make_unique<UartCommunication>(UART_NUM_1, GPIO_NUM_17, GPIO_NUM_18, 115200);
            
            if (uart_comm_->Initialize()) {
                // 设置接收回调函数（可选，用于接收另一块ESP32的响应）
                uart_comm_->SetReceiveCallback([](const std::string& response) {
                    ESP_LOGI(TAG, "Received response from remote ESP32: %s", response.c_str());
                });
                
                uart_comm_->Start();
                ESP_LOGI(TAG, "UART communication initialized successfully");
            } else {
                ESP_LOGE(TAG, "Failed to initialize UART communication");
                uart_comm_.reset();
            }
        }
    }

    // 发送LED控制命令
    bool SendLedCommand(const std::string& command) {
        if (uart_comm_ == nullptr) {
            ESP_LOGE(TAG, "UART communication not initialized");
            return false;
        }
        
        return uart_comm_->SendCommand(command);
    }

public:
    RemoteLed() : Thing("RemoteLed", "通过串口控制的远程灯"), power_(false) {
        InitializeUart();

        // 定义设备的属性
        properties_.AddBooleanProperty("power", "灯是否打开", [this]() -> bool {
            return power_;
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("TurnOn", "打开灯", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Turning on remote LED");
            if (SendLedCommand("led turn on")) {
                power_ = true;
                ESP_LOGI(TAG, "Remote LED turned on successfully");
            } else {
                ESP_LOGE(TAG, "Failed to turn on remote LED");
            }
        });

        methods_.AddMethod("TurnOff", "关闭灯", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Turning off remote LED");
            if (SendLedCommand("led turn off")) {
                power_ = false;
                ESP_LOGI(TAG, "Remote LED turned off successfully");
            } else {
                ESP_LOGE(TAG, "Failed to turn off remote LED");
            }
        });

        // 添加切换状态的方法
        methods_.AddMethod("Toggle", "切换灯状态", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Toggling remote LED");
            if (power_) {
                if (SendLedCommand("led turn off")) {
                    power_ = false;
                    ESP_LOGI(TAG, "Remote LED toggled off");
                }
            } else {
                if (SendLedCommand("led turn on")) {
                    power_ = true;
                    ESP_LOGI(TAG, "Remote LED toggled on");
                }
            }
        });
    }

    ~RemoteLed() {
        // 析构时停止UART通信
        if (uart_comm_) {
            uart_comm_->Stop();
            uart_comm_.reset();
        }
    }
};

// 静态成员定义
std::unique_ptr<UartCommunication> RemoteLed::uart_comm_ = nullptr;

} // namespace iot

DECLARE_THING(RemoteLed);
