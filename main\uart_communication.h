#ifndef UART_COMMUNICATION_H
#define UART_COMMUNICATION_H

#include <driver/uart.h>
#include <driver/gpio.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <string>
#include <functional>

class UartCommunication {
public:
    UartCommunication(uart_port_t uart_port, gpio_num_t tx_pin, gpio_num_t rx_pin, int baud_rate = 115200);
    ~UartCommunication();

    bool Initialize();
    bool SendCommand(const std::string& command);
    void SetReceiveCallback(std::function<void(const std::string&)> callback);
    void Start();
    void Stop();

private:
    uart_port_t uart_port_;
    gpio_num_t tx_pin_;
    gpio_num_t rx_pin_;
    int baud_rate_;
    TaskHandle_t receive_task_handle_;
    bool is_running_;
    std::function<void(const std::string&)> receive_callback_;

    static void ReceiveTask(void* parameter);
    void ProcessReceiveData();
};

#endif // UART_COMMUNICATION_H
