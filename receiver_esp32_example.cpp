/*
 * 接收端ESP32代码示例
 * 用于接收来自小智ESP32的串口指令并控制LED
 * 
 * 硬件连接：
 * - 接收端ESP32的GPIO_NUM_16 连接到 小智ESP32的GPIO_NUM_17 (TX)
 * - 接收端ESP32的GPIO_NUM_17 连接到 小智ESP32的GPIO_NUM_18 (RX)
 * - LED连接到接收端ESP32的GPIO_NUM_2
 * - 共地连接
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"

#define TAG "LED_RECEIVER"

// UART配置
#define UART_PORT_NUM      UART_NUM_1
#define UART_BAUD_RATE     115200
#define UART_TX_PIN        GPIO_NUM_17  // 连接到小智ESP32的RX
#define UART_RX_PIN        GPIO_NUM_16  // 连接到小智ESP32的TX

// LED配置
#define LED_GPIO           GPIO_NUM_2

// 缓冲区大小
#define BUF_SIZE           1024

static QueueHandle_t uart_queue;

// LED状态
static bool led_state = false;

// 初始化LED
void init_led() {
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << LED_GPIO),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    gpio_config(&io_conf);
    gpio_set_level(LED_GPIO, 0);
    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_GPIO);
}

// 控制LED
void control_led(bool on) {
    led_state = on;
    gpio_set_level(LED_GPIO, on ? 1 : 0);
    ESP_LOGI(TAG, "LED %s", on ? "ON" : "OFF");
}



// 处理接收到的命令
void process_command(const char* command) {
    ESP_LOGI(TAG, "Processing command: %s", command);
    
    if (strcmp(command, "led turn on") == 0) {
        control_led(true);
        // 发送响应
        uart_write_bytes(UART_PORT_NUM, "LED_ON_OK\n", strlen("LED_ON_OK\n"));
        
    } else if (strcmp(command, "led turn off") == 0) {
        control_led(false);
        // 发送响应
        uart_write_bytes(UART_PORT_NUM, "LED_OFF_OK\n", strlen("LED_OFF_OK\n"));

    } else if (strcmp(command, "led status") == 0) {
        // 查询LED状态
        char response[32];
        snprintf(response, sizeof(response), "LED_STATUS_%s\n", led_state ? "ON" : "OFF");
        uart_write_bytes(UART_PORT_NUM, response, strlen(response));
        
    } else {
        ESP_LOGW(TAG, "Unknown command: %s", command);
        uart_write_bytes(UART_PORT_NUM, "UNKNOWN_CMD\n", strlen("UNKNOWN_CMD\n"));
    }
}

// UART事件处理任务
static void uart_event_task(void *pvParameters) {
    uart_event_t event;
    uint8_t* dtmp = (uint8_t*) malloc(BUF_SIZE);
    char command_buffer[256];
    int cmd_pos = 0;
    
    while (1) {
        // 等待UART事件
        if (xQueueReceive(uart_queue, (void*)&event, (TickType_t)portMAX_DELAY)) {
            bzero(dtmp, BUF_SIZE);
            
            switch (event.type) {
                case UART_DATA:
                    // 读取数据
                    uart_read_bytes(UART_PORT_NUM, dtmp, event.size, portMAX_DELAY);
                    
                    // 处理接收到的数据
                    for (int i = 0; i < event.size; i++) {
                        char c = dtmp[i];
                        
                        if (c == '\n' || c == '\r') {
                            if (cmd_pos > 0) {
                                command_buffer[cmd_pos] = '\0';
                                process_command(command_buffer);
                                cmd_pos = 0;
                            }
                        } else if (cmd_pos < sizeof(command_buffer) - 1) {
                            command_buffer[cmd_pos++] = c;
                        }
                    }
                    break;
                    
                case UART_FIFO_OVF:
                    ESP_LOGI(TAG, "hw fifo overflow");
                    uart_flush_input(UART_PORT_NUM);
                    xQueueReset(uart_queue);
                    break;
                    
                case UART_BUFFER_FULL:
                    ESP_LOGI(TAG, "ring buffer full");
                    uart_flush_input(UART_PORT_NUM);
                    xQueueReset(uart_queue);
                    break;
                    
                case UART_BREAK:
                    ESP_LOGI(TAG, "uart rx break");
                    break;
                    
                case UART_PARITY_ERR:
                    ESP_LOGI(TAG, "uart parity error");
                    break;
                    
                case UART_FRAME_ERR:
                    ESP_LOGI(TAG, "uart frame error");
                    break;
                    
                default:
                    ESP_LOGI(TAG, "uart event type: %d", event.type);
                    break;
            }
        }
    }
    free(dtmp);
    dtmp = NULL;
    vTaskDelete(NULL);
}

// 初始化UART
void init_uart() {
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    // 安装UART驱动
    uart_driver_install(UART_PORT_NUM, BUF_SIZE * 2, BUF_SIZE * 2, 20, &uart_queue, 0);
    uart_param_config(UART_PORT_NUM, &uart_config);
    uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    
    ESP_LOGI(TAG, "UART initialized - TX:%d, RX:%d, Baud:%d", UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);
}

// 心跳任务，定期发送状态
static void heartbeat_task(void *pvParameters) {
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(30000)); // 每30秒发送一次心跳
        uart_write_bytes(UART_PORT_NUM, "HEARTBEAT\n", strlen("HEARTBEAT\n"));
        ESP_LOGI(TAG, "Heartbeat sent");
    }
}

extern "C" void app_main() {
    ESP_LOGI(TAG, "LED Receiver ESP32 Starting...");
    
    // 初始化LED
    init_led();
    
    // 初始化UART
    init_uart();
    
    // 创建UART事件处理任务
    xTaskCreate(uart_event_task, "uart_event_task", 2048, NULL, 12, NULL);
    
    // 创建心跳任务
    xTaskCreate(heartbeat_task, "heartbeat_task", 1024, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "LED Receiver ESP32 Ready!");
    ESP_LOGI(TAG, "Waiting for commands from Xiaozhi ESP32...");
    
    // 发送启动消息
    vTaskDelay(pdMS_TO_TICKS(1000));
    uart_write_bytes(UART_PORT_NUM, "RECEIVER_READY\n", strlen("RECEIVER_READY\n"));
}
